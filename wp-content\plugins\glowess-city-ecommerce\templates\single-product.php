<?php
/**
 * Glowess City Ecommerce - Tek Ürün <PERSON>fası Template
 * Bu template eklenti tarafından yüklenir ve şehir bazlı özelleştirmeler içerir
 */

defined('ABSPATH') || exit;

get_header('shop'); ?>

<div class="glowess-single-product-wrapper">
    <?php
    // Şehir hero bölümü
    $selected_city = glowess_get_selected_city();
    if ($selected_city) {
        glowess_get_template('city-hero.php');
    }
    ?>
    
    <div class="container">
        <?php while (have_posts()) : the_post(); ?>
            
            <div class="glowess-product-single">
                <?php
                global $product;
                
                // Ürünün seçili şehirde mevcut olup olmadığını kontrol et
                $available_cities = get_post_meta(get_the_ID(), '_available_cities', true);
                $is_available_in_city = false;
                
                if ($selected_city && is_array($available_cities)) {
                    $is_available_in_city = in_array($selected_city, $available_cities);
                }
                ?>
                
                <?php if (!$is_available_in_city && $selected_city): ?>
                    <div class="city-availability-notice">
                        <p><strong>Bu ürün seçtiğiniz şehirde mevcut değil.</strong></p>
                        <p>Farklı bir şehir seçmek için <a href="#" class="city-selector-trigger">buraya tıklayın</a>.</p>
                    </div>
                <?php endif; ?>
                
                <div class="product-images-section">
                    <?php
                    /**
                     * Hook: woocommerce_before_single_product_summary
                     * @hooked woocommerce_show_product_sale_flash - 10
                     * @hooked woocommerce_show_product_images - 20
                     */
                    do_action('woocommerce_before_single_product_summary');
                    ?>
                </div>
                
                <div class="product-summary-section">
                    <div class="summary entry-summary">
                        <?php
                        /**
                         * Hook: woocommerce_single_product_summary
                         * @hooked woocommerce_template_single_title - 5
                         * @hooked woocommerce_template_single_rating - 10
                         * @hooked woocommerce_template_single_price - 10
                         * @hooked woocommerce_template_single_excerpt - 20
                         * @hooked woocommerce_template_single_add_to_cart - 30
                         * @hooked woocommerce_template_single_meta - 40
                         * @hooked woocommerce_template_single_sharing - 50
                         */
                        do_action('woocommerce_single_product_summary');
                        ?>
                        
                        <?php if ($selected_city): ?>
                            <div class="delivery-info-section">
                                <?php glowess_get_template('delivery-info.php', array('city_id' => $selected_city)); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
                
                <div class="product-tabs-section">
                    <?php
                    /**
                     * Hook: woocommerce_after_single_product_summary
                     * @hooked woocommerce_output_product_data_tabs - 10
                     * @hooked woocommerce_upsell_display - 15
                     * @hooked woocommerce_output_related_products - 20
                     */
                    do_action('woocommerce_after_single_product_summary');
                    ?>
                </div>
                
                <?php if ($selected_city): ?>
                    <div class="city-related-products">
                        <h3>Bu Şehirdeki Diğer Ürünler</h3>
                        <?php
                        // Aynı şehirdeki diğer ürünleri göster
                        $related_products = glowess_get_city_products($selected_city, array(
                            'posts_per_page' => 4,
                            'post__not_in' => array(get_the_ID())
                        ));
                        
                        if ($related_products->have_posts()) {
                            echo '<div class="glowess-city-products columns-4">';
                            while ($related_products->have_posts()) {
                                $related_products->the_post();
                                glowess_get_template('product-card.php');
                            }
                            echo '</div>';
                            wp_reset_postdata();
                        }
                        ?>
                    </div>
                <?php endif; ?>
            </div>
            
        <?php endwhile; ?>
    </div>
</div>

<style>
.glowess-single-product-wrapper {
    padding: 20px 0;
}

.glowess-product-single {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 40px;
    margin-bottom: 40px;
}

.city-availability-notice {
    grid-column: 1 / -1;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.delivery-info-section {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 5px;
}

.product-tabs-section {
    grid-column: 1 / -1;
    margin-top: 40px;
}

.city-related-products {
    grid-column: 1 / -1;
    margin-top: 40px;
}

@media (max-width: 768px) {
    .glowess-product-single {
        grid-template-columns: 1fr;
        gap: 20px;
    }
}
</style>

<?php get_footer('shop'); ?>
