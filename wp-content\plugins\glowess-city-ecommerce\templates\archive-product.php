<?php
/**
 * Glowess City Ecommerce - Shop/Arşiv Sayfası Template
 * Bu template eklenti tarafından yüklenir ve şehir bazlı ürün filtrelemesi içerir
 */

defined('ABSPATH') || exit;

get_header('shop'); ?>

<div class="glowess-shop-wrapper">
    <?php
    // Şehir hero bölümü
    $selected_city = glowess_get_selected_city();
    if ($selected_city) {
        glowess_get_template('city-hero.php');
    }
    ?>
    
    <div class="container">
        <?php if ($selected_city): ?>
            <div class="city-info-bar">
                <?php
                $city = get_post($selected_city);
                $city_name = $city ? $city->post_title : 'Seçili Şehir';
                ?>
                <p><strong><?php echo esc_html($city_name); ?></strong> şehrindeki ürünler gösteriliyor. 
                <a href="#" class="city-selector-trigger">Şehir değiştir</a></p>
            </div>
        <?php else: ?>
            <div class="no-city-selected">
                <p>Daha iyi bir alışveriş deneyimi için lütfen şehrinizi seçin.</p>
                <a href="#" class="city-selector-trigger btn btn-primary">Şehir Seç</a>
            </div>
        <?php endif; ?>
        
        <header class="woocommerce-products-header">
            <?php if (apply_filters('woocommerce_show_page_title', true)) : ?>
                <h1 class="woocommerce-products-header__title page-title">
                    <?php woocommerce_page_title(); ?>
                    <?php if ($selected_city): ?>
                        <span class="city-suffix"> - <?php echo esc_html($city_name); ?></span>
                    <?php endif; ?>
                </h1>
            <?php endif; ?>

            <?php
            /**
             * Hook: woocommerce_archive_description
             * @hooked woocommerce_taxonomy_archive_description - 10
             * @hooked woocommerce_product_archive_description - 10
             */
            do_action('woocommerce_archive_description');
            ?>
        </header>
        
        <?php if (woocommerce_product_loop()) : ?>
            
            <?php
            /**
             * Hook: woocommerce_before_shop_loop
             * @hooked woocommerce_output_all_notices - 10
             * @hooked woocommerce_result_count - 20
             * @hooked woocommerce_catalog_ordering - 30
             */
            do_action('woocommerce_before_shop_loop');
            ?>
            
            <div class="glowess-products-grid">
                <?php woocommerce_product_loop_start(); ?>
                
                <?php if (wc_get_loop_prop('is_shortcode')) : ?>
                    <?php while (have_posts()) : the_post(); ?>
                        <?php
                        /**
                         * Hook: woocommerce_shop_loop
                         */
                        do_action('woocommerce_shop_loop');
                        
                        // Özel ürün kartı template'ini kullan
                        glowess_get_template('product-card.php');
                        ?>
                    <?php endwhile; ?>
                <?php else : ?>
                    <?php while (have_posts()) : the_post(); ?>
                        <?php
                        /**
                         * Hook: woocommerce_shop_loop
                         */
                        do_action('woocommerce_shop_loop');
                        
                        // Özel ürün kartı template'ini kullan
                        glowess_get_template('product-card.php');
                        ?>
                    <?php endwhile; ?>
                <?php endif; ?>
                
                <?php woocommerce_product_loop_end(); ?>
            </div>
            
            <?php
            /**
             * Hook: woocommerce_after_shop_loop
             * @hooked woocommerce_pagination - 10
             */
            do_action('woocommerce_after_shop_loop');
            ?>
            
        <?php else : ?>
            
            <?php
            /**
             * Hook: woocommerce_no_products_found
             * @hooked wc_no_products_found - 10
             */
            do_action('woocommerce_no_products_found');
            ?>
            
            <?php if ($selected_city): ?>
                <div class="no-products-city-message">
                    <h3>Bu şehirde henüz ürün bulunmuyor</h3>
                    <p>Farklı bir şehir seçerek daha fazla ürün görebilirsiniz.</p>
                    <a href="#" class="city-selector-trigger btn btn-primary">Farklı Şehir Seç</a>
                </div>
            <?php endif; ?>
            
        <?php endif; ?>
        
        <?php if ($selected_city): ?>
            <div class="city-categories-section">
                <h2>Kategoriler</h2>
                <?php glowess_get_template('city-categories.php', array('city_id' => $selected_city)); ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.glowess-shop-wrapper {
    padding: 20px 0;
}

.city-info-bar {
    background: #e3f2fd;
    padding: 10px 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    text-align: center;
}

.city-info-bar a {
    color: #1976d2;
    text-decoration: underline;
    margin-left: 10px;
}

.no-city-selected {
    background: #fff3e0;
    padding: 20px;
    border-radius: 5px;
    text-align: center;
    margin-bottom: 30px;
}

.city-suffix {
    color: #666;
    font-weight: normal;
    font-size: 0.8em;
}

.glowess-products-grid {
    margin: 20px 0;
}

.no-products-city-message {
    text-align: center;
    padding: 40px 20px;
    background: #f8f9fa;
    border-radius: 10px;
    margin: 40px 0;
}

.city-categories-section {
    margin-top: 60px;
    padding-top: 40px;
    border-top: 1px solid #eee;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    background: #007cba;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: background 0.3s;
}

.btn:hover {
    background: #005a87;
    color: white;
}

.btn-primary {
    background: #007cba;
}
</style>

<?php get_footer('shop'); ?>
