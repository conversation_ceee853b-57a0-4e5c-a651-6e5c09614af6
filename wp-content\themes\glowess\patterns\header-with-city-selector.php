<?php
/**
 * Title: Header with City Selector
 * Slug: glowess/header-with-city-selector
 * Categories: header
 * Description: Header v5 with city selector added
 *
 * @package  glowess
 */

?>

<!-- wp:group {"metadata":{"name":"Header with City Selector"},"className":"header-v5","style":{"spacing":{"blockGap":"0","padding":{"top":"0","bottom":"0"}}},"layout":{"type":"constrained"}} -->
<div class="wp-block-group header-v5" style="padding-top:0;padding-bottom:0"><!-- wp:group {"metadata":{"name":"Desktop Header"},"align":"full","className":"desktop-header","style":{"spacing":{"blockGap":"0","padding":{"top":"0px","bottom":"0px","left":"60px","right":"60px"}}},"layout":{"type":"constrained","contentSize":"100%"}} -->
<div class="wp-block-group alignfull desktop-header" style="padding-top:0px;padding-right:60px;padding-bottom:0px;padding-left:60px"><!-- wp:group {"metadata":{"name":"Top bar"},"align":"full","style":{"spacing":{"padding":{"top":"10px","bottom":"10px","left":"0","right":"0"}},"dimensions":{"minHeight":"40px"}},"backgroundColor":"secondary","layout":{"type":"constrained"}} -->
<div class="wp-block-group alignfull has-secondary-background-color has-background" style="min-height:40px;padding-top:10px;padding-right:0;padding-bottom:10px;padding-left:0"><!-- wp:group {"layout":{"type":"flex","flexWrap":"nowrap","justifyContent":"space-between"}} -->
<div class="wp-block-group"><!-- wp:paragraph {"align":"left","style":{"elements":{"link":{"color":{"text":"var:preset|color|base"}}},"typography":{"fontSize":"13px","fontStyle":"normal","fontWeight":"300"}},"textColor":"base"} -->
<p class="has-text-align-left has-base-color has-text-color has-link-color" style="font-size:13px;font-style:normal;font-weight:300"><?php echo esc_html__( 'Free worldwide shipping for orders over $50', 'glowess' ); ?></p>
<!-- /wp:paragraph -->

<!-- wp:glowess/city-selector {"style":"dropdown","showIcon":true} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"className":"relative","style":{"spacing":{"padding":{"top":"19px","bottom":"21px","left":"0px","right":"0px"}},"border":{"bottom":{"color":"#D7DAD9","width":"1px"},"top":{},"right":{},"left":{}}},"layout":{"type":"flex","flexWrap":"nowrap","justifyContent":"space-between"}} -->
<div class="wp-block-group relative" style="border-bottom-color:#D7DAD9;border-bottom-width:1px;padding-top:19px;padding-right:0px;padding-bottom:21px;padding-left:0px"><!-- wp:group {"className":"cat-pri-nav","style":{"layout":{"selfStretch":"fixed","flexSize":"770px"},"spacing":{"blockGap":"61px"}},"layout":{"type":"flex","flexWrap":"nowrap"}} -->
<div class="wp-block-group cat-pri-nav"><!-- wp:navigation {"ref":163,"textColor":"secondary","overlayBackgroundColor":"secondary","overlayTextColor":"base","metadata":{"ignoredHookedBlocks":["woocommerce/customer-account"]},"className":"catalog-menu","style":{"layout":{"selfStretch":"fit","flexSize":null},"spacing":{"blockGap":"30px"}},"fontFamily":"heading","layout":{"type":"flex","justifyContent":"left"}} /-->

<!-- wp:navigation {"ref":174,"textColor":"secondary","overlayBackgroundColor":"secondary","overlayTextColor":"base","metadata":{"ignoredHookedBlocks":["woocommerce/customer-account"]},"className":"primary-menu","style":{"layout":{"selfStretch":"fit","flexSize":null},"spacing":{"blockGap":"30px"}},"fontFamily":"heading","layout":{"type":"flex","justifyContent":"left"}} /--></div>
<!-- /wp:group -->

<!-- wp:site-title {"textAlign":"center","style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"},"layout":{"selfStretch":"fill","flexSize":null}},"fontSize":"x-large"} /-->

<!-- wp:group {"style":{"layout":{"selfStretch":"fixed","flexSize":"770px"},"spacing":{"blockGap":"30px"}},"layout":{"type":"flex","flexWrap":"nowrap","justifyContent":"right"}} -->
<div class="wp-block-group"><!-- wp:navigation {"ref":184,"textColor":"secondary","overlayBackgroundColor":"secondary","overlayTextColor":"base","metadata":{"ignoredHookedBlocks":["woocommerce/customer-account"]},"className":"currency","style":{"typography":{"textTransform":"uppercase","fontSize":"13px"}}} /-->

<!-- wp:group {"style":{"layout":{"selfStretch":"fit","flexSize":null},"spacing":{"padding":{"right":"0","left":"0"},"blockGap":"34px"}},"layout":{"type":"flex","flexWrap":"nowrap","justifyContent":"right"}} -->
<div class="wp-block-group" style="padding-right:0;padding-left:0"><!-- wp:glowess/city-selector {"style":"button","showIcon":true,"buttonText":"Şehir"} /-->

<!-- wp:image {"id":55,"width":"17px","sizeSlug":"full","linkDestination":"custom","className":"search-toggle","style":{"layout":{"selfStretch":"fit","flexSize":null}}} -->
<figure class="wp-block-image size-full is-resized search-toggle" id="showModalBtn"><a href="#"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/search.svg'; ?>" alt="" class="wp-image-55" style="width:17px"/></a></figure>
<!-- /wp:image -->

<!-- wp:woocommerce/customer-account {"displayStyle":"icon_only","iconClass":"wc-block-customer-account__account-icon"} /-->

<!-- wp:woocommerce/mini-cart {"style":{"typography":{"fontSize":"16px"}}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->

<!-- wp:group {"metadata":{"name":"Mobile Header"},"align":"full","className":"mobile-header","style":{"spacing":{"padding":{"top":"var:preset|spacing|10","bottom":"var:preset|spacing|10","left":"16px","right":"16px"},"blockGap":"14px"},"border":{"bottom":{"color":"var:preset|color|gray-100","width":"1px"},"top":[],"right":[],"left":[]}},"layout":{"type":"constrained","contentSize":"100%"}} -->
<div class="wp-block-group alignfull mobile-header" style="border-bottom-color:var(--wp--preset--color--gray-100);border-bottom-width:1px;padding-top:var(--wp--preset--spacing--10);padding-right:16px;padding-bottom:var(--wp--preset--spacing--10);padding-left:16px"><!-- wp:group {"align":"wide","layout":{"type":"flex","flexWrap":"nowrap","justifyContent":"space-between"}} -->
<div class="wp-block-group alignwide"><!-- wp:group {"style":{"spacing":{"blockGap":"12px"}},"layout":{"type":"flex","flexWrap":"nowrap"}} -->
<div class="wp-block-group"><!-- wp:navigation {"ref":174,"overlayMenu":"always","icon":"menu","metadata":{"ignoredHookedBlocks":["woocommerce/customer-account"]}} /-->

<!-- wp:glowess/city-selector {"style":"button","showIcon":true,"buttonText":"Şehir"} /--></div>
<!-- /wp:group -->

<!-- wp:site-title {"textAlign":"center","style":{"typography":{"textTransform":"uppercase","fontStyle":"normal","fontWeight":"500","lineHeight":"1.2"},"layout":{"selfStretch":"fill","flexSize":null}},"fontSize":"large"} /-->

<!-- wp:group {"style":{"spacing":{"blockGap":"12px"}},"layout":{"type":"flex","flexWrap":"nowrap"}} -->
<div class="wp-block-group"><!-- wp:image {"id":55,"width":"17px","sizeSlug":"full","linkDestination":"custom","className":"search-toggle"} -->
<figure class="wp-block-image size-full is-resized search-toggle" id="showModalBtn"><a href="#"><img src="<?php echo esc_url( get_template_directory_uri() ) . '/assets/images/search.svg'; ?>" alt="" class="wp-image-55" style="width:17px"/></a></figure>
<!-- /wp:image -->

<!-- wp:woocommerce/customer-account {"displayStyle":"icon_only","iconClass":"wc-block-customer-account__account-icon"} /-->

<!-- wp:woocommerce/mini-cart {"style":{"typography":{"fontSize":"16px"}}} /--></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group --></div>
<!-- /wp:group -->
