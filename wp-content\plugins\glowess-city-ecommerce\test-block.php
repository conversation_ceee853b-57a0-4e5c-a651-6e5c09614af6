<?php
/**
 * Test Block Registration
 * Bu dosyayı çalıştırarak block'un register edilip edilmediğini kontrol edebilirsiniz
 */

// WordPress'i yükle
require_once('../../../wp-load.php');

// Admin kontrolü
if (!current_user_can('manage_options')) {
    die('Bu sayfaya erişim yetkiniz yok.');
}

echo '<h1>Glowess City Selector Block Test</h1>';

// Registered blocks kontrol et
$registered_blocks = WP_Block_Type_Registry::get_instance()->get_all_registered();

echo '<h2>Tüm Kayıtlı Bloklar:</h2>';
echo '<p>Toplam: ' . count($registered_blocks) . ' block</p>';

// Glowess blokları ara
$glowess_blocks = array_filter($registered_blocks, function($name) {
    return strpos($name, 'glowess/') === 0;
}, ARRAY_FILTER_USE_KEY);

echo '<h2><PERSON><PERSON><PERSON>lokları:</h2>';
if (!empty($glowess_blocks)) {
    echo '<ul>';
    foreach ($glowess_blocks as $name => $block) {
        echo '<li><strong>' . esc_html($name) . '</strong></li>';
        echo '<ul>';
        echo '<li>Title: ' . (isset($block->title) ? $block->title : 'N/A') . '</li>';
        echo '<li>Category: ' . (isset($block->category) ? $block->category : 'N/A') . '</li>';
        echo '<li>Script: ' . (isset($block->editor_script) ? $block->editor_script : 'N/A') . '</li>';
        echo '</ul>';
    }
    echo '</ul>';
} else {
    echo '<p style="color: red;"><strong>Hiç Glowess bloğu bulunamadı!</strong></p>';
}

// Script kontrolü
global $wp_scripts;
echo '<h2>Script Durumu:</h2>';
echo '<p><strong>glowess-city-selector-block:</strong> ';
if (isset($wp_scripts->registered['glowess-city-selector-block'])) {
    $script = $wp_scripts->registered['glowess-city-selector-block'];
    echo 'Kayıtlı ✅</p>';
    echo '<ul>';
    echo '<li>Src: ' . $script->src . '</li>';
    echo '<li>Version: ' . $script->ver . '</li>';
    echo '<li>Dependencies: ' . implode(', ', $script->deps) . '</li>';
    echo '</ul>';
} else {
    echo 'Kayıtlı değil ❌</p>';
}

// JavaScript dosyası var mı?
$js_file = plugin_dir_path(__FILE__) . 'assets/city-selector-block.js';
echo '<h2>JavaScript Dosyası:</h2>';
echo '<p><strong>Dosya yolu:</strong> ' . $js_file . '</p>';
echo '<p><strong>Dosya var mı:</strong> ' . (file_exists($js_file) ? 'Evet ✅' : 'Hayır ❌') . '</p>';
if (file_exists($js_file)) {
    echo '<p><strong>Dosya boyutu:</strong> ' . filesize($js_file) . ' bytes</p>';
    echo '<p><strong>Son değişiklik:</strong> ' . date('Y-m-d H:i:s', filemtime($js_file)) . '</p>';
}

// Block kategorileri
echo '<h2>Block Kategorileri:</h2>';
$categories = get_default_block_categories();
if (function_exists('get_block_categories')) {
    $categories = get_block_categories(get_post());
}

echo '<ul>';
foreach ($categories as $category) {
    echo '<li><strong>' . $category['slug'] . '</strong> - ' . $category['title'] . '</li>';
}
echo '</ul>';

// Plugin aktif mi?
echo '<h2>Plugin Durumu:</h2>';
echo '<p><strong>Glowess City Ecommerce:</strong> ';
if (is_plugin_active('glowess-city-ecommerce/glowess-city-ecommerce.php')) {
    echo 'Aktif ✅</p>';
} else {
    echo 'Aktif değil ❌</p>';
}

echo '<hr>';
echo '<p><a href="' . admin_url('post-new.php') . '">Yeni Post Oluştur</a> | ';
echo '<a href="' . admin_url('edit.php?post_type=page') . '">Sayfalar</a></p>';
?>
