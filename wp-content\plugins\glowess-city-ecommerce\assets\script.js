/**
 * Glowess City Based E-Commerce - Frontend JavaScript
 * Compatible with jQuery and WordPress
 */

jQuery(document).ready(function($) {
    'use strict';
    
    // Global variables
    var cityModal = $('#city-selector-modal');
    var selectedCity = getCookie('selected_city_id');
    
    // Initialize plugin
    init();
    
    function init() {
        // Show city modal if no city selected
        if (!selectedCity && cityModal.length) {
            showCityModal();
        }
        
        // Bind events
        bindEvents();
        
        // Initialize components
        initCitySelector();
        initCategorySlider();
        initDeliveryInfo();
        initProductGrid();
    }
    
    function bindEvents() {
        // City selector trigger (for blocks and buttons)
        $(document).on('click', '.city-selector-trigger', function(e) {
            e.preventDefault();
            showCityModal();
        });

        // City selection in modal
        $(document).on('click', '.city-option', handleCitySelection);

        // City selector dropdown
        $(document).on('click', '.current-city-display', toggleCityDropdown);
        $(document).on('click', '.city-dropdown-item', handleDropdownCitySelection);
        
        // Category slider navigation
        $(document).on('click', '.glowess-city-category-item', handleCategoryClick);
        
        // Delivery info toggle
        $(document).on('click', '.glowess-city-delivery-toggle', toggleDeliveryInfo);
        
        // Add to cart buttons
        $(document).on('click', '.glowess-city-add-to-cart', handleAddToCart);
        
        // Close modal on outside click
        $(document).on('click', '.city-modal-overlay', function(e) {
            if (e.target === this) {
                closeCityModal();
            }
        });
        
        // Close dropdown on outside click
        $(document).on('click', function(e) {
            if (!$(e.target).closest('.glowess-city-selector').length) {
                $('.glowess-city-selector').removeClass('open');
                $('.city-dropdown').hide();
            }
        });
        
        // Smooth scroll for categories
        $(document).on('click', 'a[href^="#category-"]', function(e) {
            e.preventDefault();
            var target = $(this.getAttribute('href'));
            if (target.length) {
                smoothScrollTo(target);
            }
        });
    }
    
    function showCityModal() {
        cityModal.fadeIn(300);
        $('body').addClass('modal-open').css('overflow', 'hidden');
    }
    
    function closeCityModal() {
        cityModal.fadeOut(300);
        $('body').removeClass('modal-open').css('overflow', '');
    }
    
    function handleCitySelection(e) {
        e.preventDefault();
        
        var cityId = $(this).data('city-id');
        var citySlug = $(this).data('city-slug');
        
        if (!cityId) return;
        
        // Show loading
        $(this).addClass('loading');
        
        // AJAX call to save city selection
        $.ajax({
            url: glowess_city_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'select_city',
                city_id: cityId,
                city_slug: citySlug,
                nonce: glowess_city_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    // Set cookies
                    setCookie('selected_city_id', cityId, 30);
                    setCookie('selected_city_slug', citySlug, 30);
                    
                    // Close modal
                    closeCityModal();
                    
                    // Reload page to show city-specific content
                    setTimeout(function() {
                        location.reload();
                    }, 500);
                } else {
                    showError('Şehir seçimi kaydedilemedi. Lütfen tekrar deneyin.');
                }
            },
            error: function() {
                showError('Bir hata oluştu. Lütfen tekrar deneyin.');
            },
            complete: function() {
                $('.city-option').removeClass('loading');
            }
        });
    }
    
    function toggleCityDropdown(e) {
        e.preventDefault();
        e.stopPropagation();
        
        var selector = $(this).closest('.glowess-city-selector');
        var dropdown = selector.find('.city-dropdown');
        
        selector.toggleClass('open');
        dropdown.toggle();
    }
    
    function handleDropdownCitySelection(e) {
        e.preventDefault();
        
        var cityId = $(this).data('city-id');
        var citySlug = $(this).data('city-slug');
        var cityName = $(this).text();
        
        if (!cityId) return;
        
        // Update display
        $(this).closest('.glowess-city-selector').find('.city-name').text(cityName);
        
        // Close dropdown
        $('.glowess-city-selector').removeClass('open');
        $('.city-dropdown').hide();
        
        // Save selection and reload
        $.ajax({
            url: glowess_city_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'select_city',
                city_id: cityId,
                city_slug: citySlug,
                nonce: glowess_city_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    setCookie('selected_city_id', cityId, 30);
                    setCookie('selected_city_slug', citySlug, 30);
                    location.reload();
                }
            }
        });
    }
    
    function initCitySelector() {
        // Initialize any existing city selectors
        $('.glowess-city-selector').each(function() {
            var selector = $(this);
            // Additional initialization if needed
        });
    }
    
    function initCategorySlider() {
        var slider = $('.glowess-city-category-slider');
        
        if (slider.length) {
            // Add touch/swipe support for mobile
            var isDown = false;
            var startX;
            var scrollLeft;
            
            slider.on('mousedown', function(e) {
                isDown = true;
                startX = e.pageX - slider.offset().left;
                scrollLeft = slider.scrollLeft();
            });
            
            slider.on('mouseleave mouseup', function() {
                isDown = false;
            });
            
            slider.on('mousemove', function(e) {
                if (!isDown) return;
                e.preventDefault();
                var x = e.pageX - slider.offset().left;
                var walk = (x - startX) * 2;
                slider.scrollLeft(scrollLeft - walk);
            });
            
            // Touch events for mobile
            slider.on('touchstart', function(e) {
                startX = e.originalEvent.touches[0].pageX - slider.offset().left;
                scrollLeft = slider.scrollLeft();
            });
            
            slider.on('touchmove', function(e) {
                var x = e.originalEvent.touches[0].pageX - slider.offset().left;
                var walk = (x - startX) * 2;
                slider.scrollLeft(scrollLeft - walk);
            });
        }
    }
    
    function handleCategoryClick(e) {
        e.preventDefault();
        
        var categoryId = $(this).data('category-id');
        var scrollTarget = $('#category-' + categoryId);
        
        if (scrollTarget.length) {
            // Add active class
            $('.glowess-city-category-item').removeClass('active');
            $(this).addClass('active');
            
            // Smooth scroll to category section
            smoothScrollTo(scrollTarget);
        }
    }
    
    function smoothScrollTo(target, offset) {
        offset = offset || 80; // Account for fixed header
        
        $('html, body').animate({
            scrollTop: target.offset().top - offset
        }, 800, 'swing');
    }
    
    function initDeliveryInfo() {
        // Initialize delivery info accordion
        $('.glowess-city-delivery-content').hide();
    }
    
    function toggleDeliveryInfo(e) {
        e.preventDefault();
        
        var content = $(this).siblings('.glowess-city-delivery-content');
        var isOpen = content.hasClass('active');
        
        if (isOpen) {
            content.removeClass('active').slideUp(300);
            $(this).text($(this).data('closed-text') || 'Teslimat Detaylarını Göster');
        } else {
            content.addClass('active').slideDown(300);
            $(this).text($(this).data('open-text') || 'Teslimat Detaylarını Gizle');
        }
    }
    
    function initProductGrid() {
        // Initialize product grid with lazy loading
        if ('IntersectionObserver' in window) {
            var imageObserver = new IntersectionObserver(function(entries, observer) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        var img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            $('.glowess-city-product-image img.lazy').each(function() {
                imageObserver.observe(this);
            });
        }
    }
    
    function handleAddToCart(e) {
        e.preventDefault();
        
        var button = $(this);
        var productId = button.data('product-id');
        var quantity = button.data('quantity') || 1;
        
        if (!productId) {
            showError('Ürün ID bulunamadı.');
            return;
        }
        
        // Show loading state
        var originalText = button.text();
        button.text('Ekleniyor...').addClass('loading').prop('disabled', true);
        
        // AJAX add to cart
        $.ajax({
            url: wc_add_to_cart_params.ajax_url,
            type: 'POST',
            data: {
                action: 'woocommerce_add_to_cart',
                product_id: productId,
                quantity: quantity
            },
            success: function(response) {
                if (response.error) {
                    showError(response.error);
                } else {
                    // Update cart count
                    updateCartCount(response.fragments);
                    
                    // Show success message
                    showSuccess('Ürün sepete eklendi!');
                    
                    // Update button text temporarily
                    button.text('Eklendi!').removeClass('loading');
                    
                    setTimeout(function() {
                        button.text(originalText);
                    }, 2000);
                }
            },
            error: function() {
                showError('Ürün sepete eklenirken bir hata oluştu.');
            },
            complete: function() {
                button.prop('disabled', false).removeClass('loading');
                if (button.text() !== 'Eklendi!') {
                    button.text(originalText);
                }
            }
        });
    }
    
    function updateCartCount(fragments) {
        if (fragments) {
            $.each(fragments, function(key, value) {
                $(key).replaceWith(value);
            });
        }
        
        // Trigger cart update event
        $(document.body).trigger('wc_fragments_refreshed');
    }
    
    function showSuccess(message) {
        showNotification(message, 'success');
    }
    
    function showError(message) {
        showNotification(message, 'error');
    }
    
    function showNotification(message, type) {
        var notification = $('<div class="glowess-city-notification glowess-city-' + type + '">' + message + '</div>');
        
        // Remove existing notifications
        $('.glowess-city-notification').remove();
        
        // Add to page
        $('body').append(notification);
        
        // Position and show
        notification.css({
            position: 'fixed',
            top: '20px',
            right: '20px',
            zIndex: 999999,
            padding: '15px 20px',
            borderRadius: '0',
            boxShadow: '0 5px 15px rgba(0,0,0,0.2)',
            maxWidth: '300px',
            opacity: '0',
            transform: 'translateX(100%)'
        }).animate({
            opacity: 1,
            transform: 'translateX(0)'
        }, 300);
        
        // Auto remove after 5 seconds
        setTimeout(function() {
            notification.animate({
                opacity: 0,
                transform: 'translateX(100%)'
            }, 300, function() {
                notification.remove();
            });
        }, 5000);
    }
    
    // Utility functions
    function setCookie(name, value, days) {
        var expires = "";
        if (days) {
            var date = new Date();
            date.setTime(date.getTime() + (days * 24 * 60 * 60 * 1000));
            expires = "; expires=" + date.toUTCString();
        }
        document.cookie = name + "=" + (value || "") + expires + "; path=/";
    }
    
    function getCookie(name) {
        var nameEQ = name + "=";
        var ca = document.cookie.split(';');
        for (var i = 0; i < ca.length; i++) {
            var c = ca[i];
            while (c.charAt(0) == ' ') c = c.substring(1, c.length);
            if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
        }
        return null;
    }
    
    function eraseCookie(name) {
        document.cookie = name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';
    }
    
    // Product image hover effect
    $(document).on('mouseenter', '.glowess-city-product-image', function() {
        var img = $(this).find('img');
        var secondImage = img.data('hover-src');
        
        if (secondImage && secondImage !== img.attr('src')) {
            img.data('original-src', img.attr('src'));
            img.attr('src', secondImage);
        }
    });
    
    $(document).on('mouseleave', '.glowess-city-product-image', function() {
        var img = $(this).find('img');
        var originalSrc = img.data('original-src');
        
        if (originalSrc) {
            img.attr('src', originalSrc);
        }
    });
    
    // Scroll to top button
    var scrollToTopButton = $('<button id="scroll-to-top" class="glowess-city-scroll-top">↑</button>');
    $('body').append(scrollToTopButton);
    
    scrollToTopButton.css({
        position: 'fixed',
        bottom: '30px',
        right: '30px',
        width: '50px',
        height: '50px',
        backgroundColor: '#333',
        color: 'white',
        border: 'none',
        borderRadius: '0',
        cursor: 'pointer',
        fontSize: '20px',
        display: 'none',
        zIndex: 1000,
        transition: 'all 0.3s ease'
    });
    
    $(window).scroll(function() {
        if ($(this).scrollTop() > 300) {
            scrollToTopButton.fadeIn();
        } else {
            scrollToTopButton.fadeOut();
        }
    });
    
    scrollToTopButton.click(function() {
        $('html, body').animate({scrollTop: 0}, 800);
    });
    
    // Category sticky navigation
    var categoryNav = $('.glowess-city-categories');
    var categoryNavOffset = categoryNav.length ? categoryNav.offset().top : 0;
    
    $(window).scroll(function() {
        if (categoryNav.length && $(window).scrollTop() > categoryNavOffset) {
            categoryNav.addClass('sticky');
        } else {
            categoryNav.removeClass('sticky');
        }
    });
    
    // Search functionality
    function initCitySearch() {
        var searchInput = $('#city-search');
        if (searchInput.length) {
            searchInput.on('input', function() {
                var query = $(this).val().toLowerCase();
                var cities = $('.city-option');
                
                cities.each(function() {
                    var cityName = $(this).find('span').text().toLowerCase();
                    if (cityName.includes(query)) {
                        $(this).show();
                    } else {
                        $(this).hide();
                    }
                });
            });
        }
    }
    
    // Initialize search
    initCitySearch();
    
    // Cart threshold calculator
    function calculateCartThreshold() {
        var cartTotal = parseFloat($('.cart-subtotal .amount').text().replace(/[^\d.]/g, '')) || 0;
        var threshold = parseFloat(glowess_city_ajax.cart_threshold) || 500;
        var discountPercentage = parseFloat(glowess_city_ajax.discount_percentage) || 10;
        
        var remaining = threshold - cartTotal;
        var thresholdInfo = $('.glowess-city-cart-threshold');
        
        if (remaining > 0) {
            thresholdInfo.html('Sepetinize <strong>' + remaining.toFixed(2) + ' ₺</strong> daha ekleyin ve <strong>%' + discountPercentage + '</strong> indirim kazanın!');
            thresholdInfo.removeClass('achieved').addClass('remaining');
        } else {
            thresholdInfo.html('<strong>Tebrikler!</strong> Sepetinize <strong>%' + discountPercentage + '</strong> indirim uygulandı!');
            thresholdInfo.removeClass('remaining').addClass('achieved');
        }
    }
    
    // Update cart threshold on cart changes
    $(document.body).on('updated_cart_totals', calculateCartThreshold);
    $(document.body).on('wc_fragments_refreshed', calculateCartThreshold);
    
    // Initialize cart threshold
    if ($('.cart').length) {
        calculateCartThreshold();
    }
    
    // Product quick view (if needed)
    $(document).on('click', '.glowess-city-quick-view', function(e) {
        e.preventDefault();
        
        var productId = $(this).data('product-id');
        if (!productId) return;
        
        // Create quick view modal
        var quickViewModal = $('<div class="glowess-city-quick-view-modal"><div class="quick-view-content"><div class="quick-view-loading">Yükleniyor...</div></div></div>');
        $('body').append(quickViewModal);
        
        quickViewModal.fadeIn(300);
        
        // Load product data via AJAX
        $.ajax({
            url: glowess_city_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'glowess_city_quick_view',
                product_id: productId,
                nonce: glowess_city_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    quickViewModal.find('.quick-view-content').html(response.data);
                } else {
                    quickViewModal.find('.quick-view-content').html('<p>Ürün bilgileri yüklenemedi.</p>');
                }
            },
            error: function() {
                quickViewModal.find('.quick-view-content').html('<p>Bir hata oluştu.</p>');
            }
        });
        
        // Close modal
        $(document).on('click', '.glowess-city-quick-view-modal', function(e) {
            if (e.target === this) {
                $(this).fadeOut(300, function() {
                    $(this).remove();
                });
            }
        });
    });
    
    // Wishlist functionality (basic)
    $(document).on('click', '.glowess-city-wishlist', function(e) {
        e.preventDefault();
        
        var button = $(this);
        var productId = button.data('product-id');
        var wishlist = JSON.parse(localStorage.getItem('glowess_wishlist') || '[]');
        
        if (wishlist.includes(productId)) {
            // Remove from wishlist
            wishlist = wishlist.filter(function(id) { return id !== productId; });
            button.removeClass('active').attr('title', 'Favorilere Ekle');
            showSuccess('Ürün favorilerden çıkarıldı.');
        } else {
            // Add to wishlist
            wishlist.push(productId);
            button.addClass('active').attr('title', 'Favorilerden Çıkar');
            showSuccess('Ürün favorilere eklendi.');
        }
        
        localStorage.setItem('glowess_wishlist', JSON.stringify(wishlist));
        updateWishlistCount();
    });
    
    function updateWishlistCount() {
        var wishlist = JSON.parse(localStorage.getItem('glowess_wishlist') || '[]');
        var count = wishlist.length;
        $('.wishlist-count').text(count);
        
        if (count > 0) {
            $('.wishlist-count').show();
        } else {
            $('.wishlist-count').hide();
        }
    }
    
    // Initialize wishlist
    function initWishlist() {
        var wishlist = JSON.parse(localStorage.getItem('glowess_wishlist') || '[]');
        
        $('.glowess-city-wishlist').each(function() {
            var productId = $(this).data('product-id');
            if (wishlist.includes(productId)) {
                $(this).addClass('active').attr('title', 'Favorilerden Çıkar');
            }
        });
        
        updateWishlistCount();
    }
    
    initWishlist();
    
    // Price formatter
    function formatPrice(price) {
        return new Intl.NumberFormat('tr-TR', {
            style: 'currency',
            currency: 'TRY',
            minimumFractionDigits: 2
        }).format(price);
    }
    
    // Delivery time calculator
    function calculateDeliveryTime() {
        var selectedCity = getCookie('selected_city_slug');
        var deliveryTimes = {
            'istanbul': '30-60 dakika',
            'ankara': '45-90 dakika',
            'izmir': '45-90 dakika'
        };
        
        var estimatedTime = deliveryTimes[selectedCity] || '2-3 saat';
        $('.estimated-delivery-time').text(estimatedTime);
    }
    
    // Initialize delivery time
    calculateDeliveryTime();
    
    // Form validation
    function validateCityForm(form) {
        var isValid = true;
        var requiredFields = form.find('[required]');
        
        requiredFields.each(function() {
            var field = $(this);
            var value = field.val().trim();
            
            if (!value) {
                field.addClass('error');
                isValid = false;
            } else {
                field.removeClass('error');
            }
        });
        
        return isValid;
    }
    
    // Auto-complete for addresses (if Google Maps API available)
    function initAddressAutocomplete() {
        if (typeof google !== 'undefined' && google.maps && google.maps.places) {
            var addressInput = document.getElementById('billing_address_1');
            if (addressInput) {
                var autocomplete = new google.maps.places.Autocomplete(addressInput);
                autocomplete.setComponentRestrictions({'country': ['tr']});
            }
        }
    }
    
    // Call address autocomplete on checkout page
    if ($('body').hasClass('woocommerce-checkout')) {
        initAddressAutocomplete();
    }
    
    // Mobile menu integration
    function initMobileMenu() {
        var mobileMenu = $('.glowess-city-mobile-menu');
        if (mobileMenu.length) {
            // Mobile-specific functionality
        }
    }
    
    initMobileMenu();
    
    // Performance optimization
    function debounce(func, wait, immediate) {
        var timeout;
        return function() {
            var context = this, args = arguments;
            var later = function() {
                timeout = null;
                if (!immediate) func.apply(context, args);
            };
            var callNow = immediate && !timeout;
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
            if (callNow) func.apply(context, args);
        };
    }
    
    // Optimized scroll handler
    var optimizedScroll = debounce(function() {
        // Scroll-based functionality
        var scrollTop = $(window).scrollTop();
        
        // Update scroll progress
        var scrollProgress = (scrollTop / ($(document).height() - $(window).height())) * 100;
        $('.scroll-progress').css('width', scrollProgress + '%');
        
    }, 10);
    
    $(window).scroll(optimizedScroll);
    
    // Image lazy loading fallback
    function lazyLoadImages() {
        $('.lazy').each(function() {
            var img = $(this);
            var src = img.data('src');
            
            if (src && isElementInViewport(img[0])) {
                img.attr('src', src).removeClass('lazy');
            }
        });
    }
    
    function isElementInViewport(el) {
        var rect = el.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }
    
    // Initialize lazy loading fallback
    $(window).on('scroll resize', debounce(lazyLoadImages, 100));
    lazyLoadImages();
    
    // Analytics integration
    function trackCitySelection(cityName, cityId) {
        // Google Analytics 4
        if (typeof gtag !== 'undefined') {
            gtag('event', 'city_selected', {
                'city_name': cityName,
                'city_id': cityId,
                'event_category': 'Location',
                'event_label': cityName
            });
        }
        
        // Facebook Pixel
        if (typeof fbq !== 'undefined') {
            fbq('track', 'CitySelected', {
                city_name: cityName,
                city_id: cityId
            });
        }
    }
    
    // Track product interactions
    function trackProductInteraction(action, productId, productName) {
        if (typeof gtag !== 'undefined') {
            gtag('event', action, {
                'product_id': productId,
                'product_name': productName,
                'event_category': 'Product',
                'event_label': productName
            });
        }
    }
    
    // Console welcome message
    console.log('%cGlowess City Plugin', 'color: #333; font-size: 16px; font-weight: bold;');
    console.log('%cŞehir bazlı e-ticaret sistemi aktif.', 'color: #666; font-size: 12px;');
    
    // Debug mode
    if (window.location.hash === '#debug' || window.glowess_debug) {
        window.glowessCityDebug = {
            selectedCity: selectedCity,
            cookies: document.cookie,
            version: '1.0.0',
            reinit: init,
            clearCache: function() {
                eraseCookie('selected_city_id');
                eraseCookie('selected_city_slug');
                localStorage.removeItem('glowess_wishlist');
                location.reload();
            }
        };
        
        console.log('Debug mode aktif. window.glowessCityDebug objesini kullanabilirsiniz.');
    }
});

// jQuery dışında çalışacak fonksiyonlar
(function() {
    'use strict';
    
    // Page visibility API
    var hidden = "hidden";
    var visibilityChange = "visibilitychange";
    
    if (typeof document.hidden !== "undefined") {
        hidden = "hidden";
        visibilityChange = "visibilitychange";
    } else if (typeof document.msHidden !== "undefined") {
        hidden = "msHidden";
        visibilityChange = "msvisibilitychange";
    } else if (typeof document.webkitHidden !== "undefined") {
        hidden = "webkitHidden";
        visibilityChange = "webkitvisibilitychange";
    }
    
    function handleVisibilityChange() {
        if (document[hidden]) {
            // Page is hidden
        } else {
            // Page is visible
        }
    }
    
    if (typeof document.addEventListener !== "undefined" && hidden !== undefined) {
        document.addEventListener(visibilityChange, handleVisibilityChange, false);
    }
    
    // Service Worker registration (for future PWA features)
    if ('serviceWorker' in navigator) {
        window.addEventListener('load', function() {
            // navigator.registerServiceWorker('/sw.js') - future implementation
        });
    }
    
    // Critical CSS loading
    function loadCSS(href) {
        var link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = href;
        link.media = 'print';
        link.onload = function() {
            this.media = 'all';
        };
        document.head.appendChild(link);
    }
    
    // Load non-critical CSS
    setTimeout(function() {
        // loadCSS('/path/to/non-critical.css');
    }, 100);
    
})();