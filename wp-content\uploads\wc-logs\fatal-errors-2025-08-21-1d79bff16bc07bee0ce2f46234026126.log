2025-08-21T01:37:53+00:00 CRITICAL Uncaught TypeError: call_user_func_array(): Argument #1 ($callback) must be a valid callback, class GlowessCityEcommerce does not have a method "template_loader" in C:\laragon\www2\wordpress\wp-includes\class-wp-hook.php:324 CONTEXT: {"error":{"type":1,"file":"C:\laragon\www2\wordpress\wp-includes\class-wp-hook.php","line":324},"remote-logging":true,"backtrace":["#0 C:\laragon\www2\wordpress\wp-includes\plugin.php(205): WP_Hook->apply_filters('C:\\laragon\\www2...', Array)n#1 C:\laragon\www2\wordpress\wp-includes\template-loader.php(104): apply_filters('template_includ...', 'C:\\laragon\\www2...')n#2 C:\laragon\www2\wordpress\wp-blog-header.php(19): require_once('C:\\laragon\\www2...')n#3 C:\laragon\www2\wordpress\index.php(17): require('C:\\laragon\\www2...')n#4 {main}n  thrown"]}
2025-08-21T02:15:19+00:00 CRITICAL Uncaught TypeError: call_user_func_array(): Argument #1 ($callback) must be a valid callback, class GlowessCityEcommerce does not have a method "filter_all_product_queries" in C:\laragon\www2\wordpress2\wp-includes\class-wp-hook.php:324 CONTEXT: {"error":{"type":1,"file":"C:\laragon\www2\wordpress2\wp-includes\class-wp-hook.php","line":324},"remote-logging":true,"backtrace":["#0 C:\laragon\www2\wordpress2\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)n#1 C:\laragon\www2\wordpress2\wp-includes\plugin.php(565): WP_Hook->do_action(Array)n#2 C:\laragon\www2\wordpress2\wp-includes\class-wp-query.php(1910): do_action_ref_array('pre_get_posts', Array)n#3 C:\laragon\www2\wordpress2\wp-includes\class-wp-query.php(3949): WP_Query->get_posts()n#4 C:\laragon\www2\wordpress2\wp-includes\class-wp-theme-json-resolver.php(517): WP_Query->query(Array)n#5 C:\laragon\www2\wordpress2\wp-includes\class-wp-theme-json-resolver.php(557): WP_Theme_JSON_Resolver::get_user_data_from_wp_global_styles(Object(WP_Theme))n#6 C:\laragon\www2\wordpress2\wp-includes\class-wp-theme-json-resolver.php(673): WP_Theme_JSON_Resolver::get_user_data()n#7 C:\laragon\www2\wordpress2\wp-includes\global-styles-and-settings.php(80): WP_Theme_JSON_Resolver::get_merged_data('custom')n#8 C:\laragon\www2\wordpress2\wp-content\plugins\woocommerce\src\Blocks\Templates\ComingSoonTemplate.php(85): wp_get_global_settings()n#9 C:\laragon\www2\wordpress2\wp-content\plugins\woocommerce\patterns\coming-soon-entire-site.php(19): Automattic\WooCommerce\Blocks\Templates\ComingSoonTemplate::get_font_families()n#10 C:\laragon\www2\wordpress2\wp-content\plugins\woocommerce\src\Blocks\BlockPatterns.php(95): include('C:\\laragon\\www2...')n#11 C:\laragon\www2\wordpress2\wp-content\plugins\woocommerce\src\Blocks\BlockPatterns.php(122): Automattic\WooCommerce\Blocks\BlockPatterns->load_pattern_content('C:\\laragon\\www2...')n#12 C:\laragon\www2\wordpress2\wp-includes\class-wp-hook.php(324): Automattic\WooCommerce\Blocks\BlockPatterns->register_block_patterns('')n#13 C:\laragon\www2\wordpress2\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters(NULL, Array)n#14 C:\laragon\www2\wordpress2\wp-includes\plugin.php(517): WP_Hook->do_action(Array)n#15 C:\laragon\www2\wordpress2\wp-settings.php(727): do_action('init')n#16 C:\laragon\www2\wordpress2\wp-config.php(103): require_once('C:\\laragon\\www2...')n#17 C:\laragon\www2\wordpress2\wp-load.php(50): require_once('C:\\laragon\\www2...')n#18 C:\laragon\www2\wordpress2\wp-blog-header.php(13): require_once('C:\\laragon\\www2...')n#19 C:\laragon\www2\wordpress2\index.php(17): require('C:\\laragon\\www2...')n#20 {main}n  thrown"]}
