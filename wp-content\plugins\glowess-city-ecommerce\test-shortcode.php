<?php
/**
 * Test Shortcode Page
 * Bu sayfada shortcode'u test edebilirsiniz
 */

// WordPress'i yükle
require_once('../../../wp-load.php');

// Header
get_header();
?>

<div style="padding: 40px; max-width: 800px; margin: 0 auto;">
    <h1>Şehir Seçici Test Sayfası</h1>
    
    <h2>1. Button Style</h2>
    <p>Shortcode: <code>[city_selector style="button" show_icon="true" button_text="Şehir Seç"]</code></p>
    <div style="padding: 20px; border: 1px solid #ddd; margin: 10px 0;">
        <?php echo do_shortcode('[city_selector style="button" show_icon="true" button_text="Şehir Seç"]'); ?>
    </div>
    
    <h2>2. Dropdown Style</h2>
    <p>Shortcode: <code>[city_selector style="dropdown" show_icon="true"]</code></p>
    <div style="padding: 20px; border: 1px solid #ddd; margin: 10px 0;">
        <?php echo do_shortcode('[city_selector style="dropdown" show_icon="true"]'); ?>
    </div>
    
    <h2>3. Header Style (Küçük)</h2>
    <p>Header için optimize edilmiş versiyon:</p>
    <div style="padding: 20px; border: 1px solid #ddd; margin: 10px 0; background: #f5f5f5;">
        <div class="header-v5">
            <?php echo do_shortcode('[city_selector style="button" show_icon="true" button_text="Şehir"]'); ?>
        </div>
    </div>
    
    <h2>4. Debug Bilgileri</h2>
    <?php
    $selected_city = glowess_get_selected_city();
    if ($selected_city) {
        $city = get_post($selected_city);
        echo '<p><strong>Seçili Şehir:</strong> ' . ($city ? $city->post_title : 'Bulunamadı') . ' (ID: ' . $selected_city . ')</p>';
    } else {
        echo '<p><strong>Seçili Şehir:</strong> Yok</p>';
    }
    ?>
    
    <h2>5. JavaScript Test</h2>
    <p>Browser console'u açın (F12) ve butona tıklayın. Modal açılmalı.</p>
    
    <script>
    jQuery(document).ready(function($) {
        console.log('Test sayfası yüklendi');
        console.log('jQuery version:', $.fn.jquery);
        console.log('City modal element:', $('#city-selector-modal').length);
        
        // Test click handler
        $('.city-selector-trigger').on('click', function() {
            console.log('City selector trigger clicked!');
        });
    });
    </script>
    
    <hr>
    <p><a href="<?php echo admin_url('post-new.php'); ?>">Yeni Post Oluştur</a> | 
    <a href="<?php echo home_url(); ?>">Ana Sayfa</a></p>
</div>

<?php
get_footer();
?>
