(function(blocks, element, editor) {
    var el = element.createElement;
    var InspectorControls = editor.InspectorControls;
    var PanelBody = wp.components.PanelBody;
    var SelectControl = wp.components.SelectControl;
    var ToggleControl = wp.components.ToggleControl;
    var TextControl = wp.components.TextControl;

    blocks.registerBlockType('glowess/city-selector', {
        title: 'Şehir Seçici',
        description: 'Kullanıcıların şehir seçmesi için buton veya dropdown.',
        icon: 'location-alt',
        category: 'widgets',
        attributes: {
            style: {
                type: 'string',
                default: 'button'
            },
            showIcon: {
                type: 'boolean',
                default: true
            },
            buttonText: {
                type: 'string',
                default: '<PERSON>ehir Seç'
            }
        },

        edit: function(props) {
            var attributes = props.attributes;
            var setAttributes = props.setAttributes;

            function onStyleChange(newStyle) {
                setAttributes({ style: newStyle });
            }

            function onShowIconChange(newShowIcon) {
                setAttributes({ showIcon: newShowIcon });
            }

            function onButtonTextChange(newButtonText) {
                setAttributes({ buttonText: newButtonText });
            }

            // Preview component
            var previewElement;
            if (attributes.style === 'dropdown') {
                previewElement = el('div', {
                    className: 'glowess-city-selector-block dropdown-style',
                    style: {
                        display: 'inline-flex',
                        alignItems: 'center',
                        gap: '8px',
                        padding: '8px 12px',
                        border: '1px solid #ddd',
                        borderRadius: '4px',
                        backgroundColor: '#f9f9f9',
                        cursor: 'pointer'
                    }
                }, [
                    attributes.showIcon && el('span', {
                        key: 'icon',
                        className: 'city-icon'
                    }, '📍'),
                    el('span', {
                        key: 'text',
                        style: { marginRight: '8px' }
                    }, 'Şehir Seç'),
                    el('span', {
                        key: 'arrow',
                        className: 'dropdown-arrow'
                    }, '▼')
                ]);
            } else {
                previewElement = el('button', {
                    className: 'glowess-city-selector-block button-style',
                    style: {
                        display: 'inline-flex',
                        alignItems: 'center',
                        gap: '8px',
                        padding: '10px 16px',
                        backgroundColor: '#007cba',
                        color: 'white',
                        border: 'none',
                        borderRadius: '4px',
                        cursor: 'pointer',
                        fontSize: '14px'
                    }
                }, [
                    attributes.showIcon && el('span', {
                        key: 'icon',
                        className: 'city-icon'
                    }, '📍'),
                    el('span', {
                        key: 'text'
                    }, attributes.buttonText)
                ]);
            }

            return [
                el(InspectorControls, { key: 'inspector' }, [
                    el(PanelBody, {
                        key: 'settings',
                        title: 'Şehir Seçici Ayarları',
                        initialOpen: true
                    }, [
                        el(SelectControl, {
                            key: 'style',
                            label: 'Stil',
                            value: attributes.style,
                            options: [
                                { label: 'Buton', value: 'button' },
                                { label: 'Dropdown', value: 'dropdown' }
                            ],
                            onChange: onStyleChange
                        }),
                        el(ToggleControl, {
                            key: 'showIcon',
                            label: 'İkon Göster',
                            checked: attributes.showIcon,
                            onChange: onShowIconChange
                        }),
                        attributes.style === 'button' && el(TextControl, {
                            key: 'buttonText',
                            label: 'Buton Metni',
                            value: attributes.buttonText,
                            onChange: onButtonTextChange
                        })
                    ])
                ]),
                el('div', {
                    key: 'preview',
                    className: 'glowess-city-selector-preview',
                    style: {
                        padding: '20px',
                        border: '2px dashed #ddd',
                        borderRadius: '4px',
                        textAlign: 'center',
                        backgroundColor: '#fafafa'
                    }
                }, [
                    el('p', {
                        key: 'label',
                        style: {
                            margin: '0 0 10px 0',
                            fontSize: '12px',
                            color: '#666',
                            textTransform: 'uppercase',
                            letterSpacing: '1px'
                        }
                    }, 'Şehir Seçici Önizleme'),
                    previewElement
                ])
            ];
        },

        save: function() {
            // Server-side rendering kullanıyoruz
            return null;
        }
    });

})(
    window.wp.blocks,
    window.wp.element,
    window.wp.editor || window.wp.blockEditor
);
