// Glowess City Selector Block
(function() {
    'use strict';

    // WordPress dependencies
    const { registerBlockType } = wp.blocks;
    const { createElement: el } = wp.element;
    const { InspectorControls } = wp.blockEditor;
    const { PanelBody, SelectControl, ToggleControl, TextControl } = wp.components;

    console.log('Glowess City Selector Block loading...');

    registerBlockType('glowess/city-selector', {
        title: 'Şehir Seçici',
        description: 'Kullanıcıların şehir seçmesi için buton veya dropdown.',
        icon: 'location-alt',
        category: 'widgets',
        attributes: {
            style: {
                type: 'string',
                default: 'button'
            },
            showIcon: {
                type: 'boolean',
                default: true
            },
            buttonText: {
                type: 'string',
                default: 'Şehir Seç'
            }
        },

        edit: function(props) {
            const { attributes, setAttributes } = props;

            const onStyleChange = (newStyle) => {
                setAttributes({ style: newStyle });
            };

            const onShowIconChange = (newShowIcon) => {
                setAttributes({ showIcon: newShowIcon });
            };

            const onButtonTextChange = (newButtonText) => {
                setAttributes({ buttonText: newButtonText });
            };

            // Basit preview
            const previewElement = el('div', {
                style: {
                    padding: '10px',
                    border: '1px solid #ddd',
                    borderRadius: '4px',
                    backgroundColor: '#f9f9f9',
                    textAlign: 'center'
                }
            }, [
                el('span', { key: 'icon' }, '📍 '),
                el('span', { key: 'text' }, attributes.style === 'dropdown' ? 'Şehir Seç ▼' : attributes.buttonText)
            ]);

            return [
                // Inspector Controls
                el(InspectorControls, { key: 'inspector' },
                    el(PanelBody, {
                        title: 'Şehir Seçici Ayarları',
                        initialOpen: true
                    }, [
                        el(SelectControl, {
                            label: 'Stil',
                            value: attributes.style,
                            options: [
                                { label: 'Buton', value: 'button' },
                                { label: 'Dropdown', value: 'dropdown' }
                            ],
                            onChange: onStyleChange
                        }),
                        el(ToggleControl, {
                            label: 'İkon Göster',
                            checked: attributes.showIcon,
                            onChange: onShowIconChange
                        }),
                        attributes.style === 'button' && el(TextControl, {
                            label: 'Buton Metni',
                            value: attributes.buttonText,
                            onChange: onButtonTextChange
                        })
                    ])
                ),
                // Preview
                el('div', {
                    key: 'preview',
                    style: {
                        padding: '20px',
                        border: '2px dashed #ddd',
                        borderRadius: '4px',
                        textAlign: 'center',
                        backgroundColor: '#fafafa'
                    }
                }, [
                    el('p', {
                        style: { margin: '0 0 10px 0', fontSize: '12px', color: '#666' }
                    }, 'Şehir Seçici Önizleme'),
                    previewElement
                ])
            ];
        },

        save: function() {
            // Server-side rendering kullanıyoruz
            return null;
        }
    });

    console.log('Glowess City Selector Block registered successfully!');

})();
