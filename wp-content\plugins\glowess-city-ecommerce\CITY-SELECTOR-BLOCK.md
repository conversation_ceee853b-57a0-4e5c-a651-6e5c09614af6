# Şehir Seçici Block Kullanım Kılavuzu

## 🎯 Gutenberg Block Eklendi!

Artık header'a veya herhangi bir yere şehir seçici eklemek çok kolay!

## 📦 Block Özellikleri

### Block Adı: **Şehir Seçici**
- **Kategori:** Widgets
- **İkon:** 📍 Location
- **Namespace:** `glowess/city-selector`

## 🎨 Stil Seçenekleri

### 1. **Button Style** (Varsayılan)
```
Görünüm: [📍 İstanbul] (<PERSON><PERSON> buton)
```

### 2. **Dropdown Style**
```
Görünüm: 📍 İstanbul ▼ (<PERSON>az dropdown)
```

## ⚙️ Block Ayarları

### **Stil**
- `Button` - Mavi buton görünümü
- `Dropdown` - Dropdown görünümü

### **İkon Göster**
- `Açık` - 📍 ikonu gösterir
- `Kapalı` - <PERSON><PERSON><PERSON> metin

### **But<PERSON>ni** (<PERSON><PERSON><PERSON> stil<PERSON>)
- Varsayılan: "Şehir Seç"
- Özelleştirilebilir

## 🔧 Kullanım Yöntemleri

### 1. **Gutenberg Editor'da**

1. **Block ekle** (+) butonuna tıklayın
2. **"Şehir Seçici"** arayın
3. **Block'u seçin**
4. **Sağ panelden ayarları yapın**

### 2. **Header Template'inde**

Mevcut header template'inizi düzenleyin:

```html
<!-- wp:glowess/city-selector {"style":"button","showIcon":true,"buttonText":"Şehir"} /-->
```

### 3. **Shortcode ile**

```php
[city_selector style="button" show_icon="true" button_text="Şehir Seç"]
```

### 4. **PHP'de**

```php
echo do_shortcode('[city_selector style="dropdown" show_icon="true"]');
```

## 📍 Header'a Ekleme Örnekleri

### **Örnek 1: Top Bar'a Dropdown**
```html
<!-- wp:group {"layout":{"type":"flex","justifyContent":"space-between"}} -->
<div class="wp-block-group">
    <!-- wp:paragraph -->
    <p>Free shipping over $50</p>
    <!-- /wp:paragraph -->
    
    <!-- wp:glowess/city-selector {"style":"dropdown"} /-->
</div>
<!-- /wp:group -->
```

### **Örnek 2: Navigation'a Button**
```html
<!-- wp:group {"layout":{"type":"flex","flexWrap":"nowrap"}} -->
<div class="wp-block-group">
    <!-- wp:glowess/city-selector {"style":"button","buttonText":"Şehir"} /-->
    
    <!-- wp:image {"src":"search-icon.svg"} /-->
    <!-- wp:woocommerce/customer-account /-->
    <!-- wp:woocommerce/mini-cart /-->
</div>
<!-- /wp:group -->
```

## 🎨 CSS Özelleştirme

### **Button Style**
```css
.glowess-city-selector-block.button-style .city-button-btn {
    background: #007cba;
    color: white;
    padding: 10px 16px;
    border-radius: 4px;
}
```

### **Dropdown Style**
```css
.glowess-city-selector-block.dropdown-style .city-dropdown-btn {
    background: white;
    border: 1px solid #ddd;
    padding: 8px 12px;
    border-radius: 4px;
}
```

## 📱 Responsive Davranış

- **Desktop:** Tam boyut
- **Mobile:** Küçültülmüş padding ve font

## 🔄 Otomatik Güncelleme

Block otomatik olarak:
- ✅ Seçili şehri gösterir
- ✅ Modal'ı açar
- ✅ Şehir değiştiğinde güncellenir
- ✅ Cookie'den şehir bilgisini alır

## 🧪 Test Etme

1. **Block'u ekleyin**
2. **Önizleme yapın**
3. **Şehir seçin**
4. **Sayfayı yenileyin** - Seçili şehir görünmeli

## 📋 Hazır Template

`wp-content/themes/glowess/patterns/header-with-city-selector.php` dosyasında hazır bir header template'i var.

Bu template'i kullanmak için:
1. **Appearance > Theme Editor**
2. **Header template'ini düzenle**
3. **Pattern'i kullan:** `<!-- wp:pattern {"slug":"glowess/header-with-city-selector"} /-->`

## 🎯 Sonuç

Artık header'ınızda şehir seçici var! Kullanıcılar kolayca şehir değiştirebilir ve ürünler otomatik filtrelenecek.

**Kolay kullanım:** Sadece block'u sürükle-bırak! 🎉
