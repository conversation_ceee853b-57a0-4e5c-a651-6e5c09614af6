<?php
/**
 * Glowess City Ecommerce - Ürün Kategorisi Template
 * Bu template eklenti tarafından yüklenir ve şehir bazlı kategori filtrelemesi içerir
 */

defined('ABSPATH') || exit;

get_header('shop'); ?>

<div class="glowess-category-wrapper">
    <?php
    // Şehir hero bölümü
    $selected_city = glowess_get_selected_city();
    if ($selected_city) {
        glowess_get_template('city-hero.php');
    }
    ?>
    
    <div class="container">
        <?php
        $category = get_queried_object();
        $category_name = $category->name;
        $category_description = $category->description;
        ?>
        
        <?php if ($selected_city): ?>
            <div class="city-category-info-bar">
                <?php
                $city = get_post($selected_city);
                $city_name = $city ? $city->post_title : 'Seçili Şehir';
                ?>
                <p><strong><?php echo esc_html($city_name); ?></strong> şehrindeki 
                <strong><?php echo esc_html($category_name); ?></strong> ürünleri gösteriliyor. 
                <a href="#" class="city-selector-trigger">Şehir değiştir</a></p>
            </div>
        <?php endif; ?>
        
        <header class="woocommerce-products-header">
            <h1 class="woocommerce-products-header__title page-title">
                <?php echo esc_html($category_name); ?>
                <?php if ($selected_city): ?>
                    <span class="city-suffix"> - <?php echo esc_html($city_name); ?></span>
                <?php endif; ?>
            </h1>
            
            <?php if ($category_description): ?>
                <div class="category-description">
                    <?php echo wp_kses_post($category_description); ?>
                </div>
            <?php endif; ?>
        </header>
        
        <?php if (woocommerce_product_loop()) : ?>
            
            <?php
            /**
             * Hook: woocommerce_before_shop_loop
             * @hooked woocommerce_output_all_notices - 10
             * @hooked woocommerce_result_count - 20
             * @hooked woocommerce_catalog_ordering - 30
             */
            do_action('woocommerce_before_shop_loop');
            ?>
            
            <div class="glowess-category-products">
                <?php woocommerce_product_loop_start(); ?>
                
                <?php while (have_posts()) : the_post(); ?>
                    <?php
                    /**
                     * Hook: woocommerce_shop_loop
                     */
                    do_action('woocommerce_shop_loop');
                    
                    // Özel ürün kartı template'ini kullan
                    glowess_get_template('product-card.php');
                    ?>
                <?php endwhile; ?>
                
                <?php woocommerce_product_loop_end(); ?>
            </div>
            
            <?php
            /**
             * Hook: woocommerce_after_shop_loop
             * @hooked woocommerce_pagination - 10
             */
            do_action('woocommerce_after_shop_loop');
            ?>
            
        <?php else : ?>
            
            <div class="no-products-found">
                <h3>Bu kategoride ürün bulunamadı</h3>
                
                <?php if ($selected_city): ?>
                    <p><strong><?php echo esc_html($city_name); ?></strong> şehrinde 
                    <strong><?php echo esc_html($category_name); ?></strong> kategorisinde henüz ürün bulunmuyor.</p>
                    
                    <div class="category-suggestions">
                        <p>Şunları deneyebilirsiniz:</p>
                        <ul>
                            <li><a href="#" class="city-selector-trigger">Farklı bir şehir seçin</a></li>
                            <li><a href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>">Tüm ürünleri görüntüleyin</a></li>
                        </ul>
                    </div>
                <?php else: ?>
                    <p>Bu kategoride henüz ürün bulunmuyor.</p>
                    <a href="#" class="city-selector-trigger btn btn-primary">Şehir Seçerek Daha Fazla Ürün Görün</a>
                <?php endif; ?>
            </div>
            
        <?php endif; ?>
        
        <?php if ($selected_city): ?>
            <!-- Aynı şehirdeki diğer kategoriler -->
            <div class="related-categories-section">
                <h2><?php echo esc_html($city_name); ?> Şehrindeki Diğer Kategoriler</h2>
                <?php
                // Şehirdeki diğer kategorileri getir
                $other_categories = get_terms(array(
                    'taxonomy' => 'product_cat',
                    'hide_empty' => true,
                    'exclude' => array($category->term_id),
                    'number' => 6
                ));
                
                if ($other_categories && !is_wp_error($other_categories)): ?>
                    <div class="categories-grid">
                        <?php foreach ($other_categories as $cat): ?>
                            <div class="category-item">
                                <a href="<?php echo esc_url(get_term_link($cat)); ?>">
                                    <?php
                                    $thumbnail_id = get_term_meta($cat->term_id, 'thumbnail_id', true);
                                    if ($thumbnail_id) {
                                        echo wp_get_attachment_image($thumbnail_id, 'woocommerce_thumbnail');
                                    }
                                    ?>
                                    <h4><?php echo esc_html($cat->name); ?></h4>
                                    <span class="product-count"><?php echo $cat->count; ?> ürün</span>
                                </a>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php endif; ?>
            </div>
        <?php endif; ?>
    </div>
</div>

<style>
.glowess-category-wrapper {
    padding: 20px 0;
}

.city-category-info-bar {
    background: #e8f5e8;
    padding: 10px 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    text-align: center;
}

.city-category-info-bar a {
    color: #2e7d32;
    text-decoration: underline;
    margin-left: 10px;
}

.city-suffix {
    color: #666;
    font-weight: normal;
    font-size: 0.8em;
}

.category-description {
    margin: 15px 0;
    color: #666;
}

.glowess-category-products {
    margin: 20px 0;
}

.no-products-found {
    text-align: center;
    padding: 40px 20px;
    background: #f8f9fa;
    border-radius: 10px;
    margin: 40px 0;
}

.category-suggestions {
    margin-top: 20px;
}

.category-suggestions ul {
    list-style: none;
    padding: 0;
}

.category-suggestions li {
    margin: 10px 0;
}

.related-categories-section {
    margin-top: 60px;
    padding-top: 40px;
    border-top: 1px solid #eee;
}

.categories-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.category-item {
    text-align: center;
    padding: 20px;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s;
}

.category-item:hover {
    transform: translateY(-5px);
}

.category-item a {
    text-decoration: none;
    color: inherit;
}

.category-item img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 5px;
    margin-bottom: 10px;
}

.category-item h4 {
    margin: 10px 0 5px;
    color: #333;
}

.product-count {
    color: #666;
    font-size: 0.9em;
}

.btn {
    display: inline-block;
    padding: 10px 20px;
    background: #007cba;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    transition: background 0.3s;
}

.btn:hover {
    background: #005a87;
    color: white;
}
</style>

<?php get_footer('shop'); ?>
